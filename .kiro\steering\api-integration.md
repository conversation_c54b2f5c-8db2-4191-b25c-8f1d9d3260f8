---
inclusion: fileMatch
fileMatchPattern: '*openai*.py'
---

# Руководство по интеграции с API

## OpenAI API

### Настройка клиента
```python
import openai
from openai import OpenAI
import os
from typing import Optional, Dict, Any

class OpenAIClient:
    """Клиент для работы с OpenAI API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Инициализация клиента OpenAI.
        
        Args:
            api_key: API ключ OpenAI. Если не указан, берется из переменной окружения.
        """
        self.client = OpenAI(
            api_key=api_key or os.getenv('OPENAI_API_KEY')
        )
        
    def analyze_image(self, image_path: str, prompt: str = None) -> str:
        """Анализ изображения с помощью GPT-4 Vision.
        
        Args:
            image_path: Путь к изображению
            prompt: Дополнительный промпт для анализа
            
        Returns:
            Описание изображения
        """
        try:
            with open(image_path, "rb") as image_file:
                response = self.client.chat.completions.create(
                    model="gpt-4-vision-preview",
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": prompt or "Опишите это изображение подробно"
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64.b64encode(image_file.read()).decode()}"
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens=500
                )
                
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Ошибка анализа изображения: {e}")
            raise
```

### Обработка ошибок API
```python
from openai import OpenAIError, RateLimitError, APITimeoutError
import time
import random

def retry_with_backoff(func, max_retries: int = 3):
    """Декоратор для повторных попыток с экспоненциальной задержкой."""
    def wrapper(*args, **kwargs):
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except RateLimitError:
                if attempt == max_retries - 1:
                    raise
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"Rate limit exceeded, waiting {wait_time:.2f}s")
                time.sleep(wait_time)
            except APITimeoutError:
                if attempt == max_retries - 1:
                    raise
                logger.warning(f"API timeout, retrying attempt {attempt + 1}")
                time.sleep(1)
            except OpenAIError as e:
                logger.error(f"OpenAI API error: {e}")
                raise
    return wrapper

@retry_with_backoff
def safe_api_call(client, **kwargs):
    """Безопасный вызов OpenAI API с повторными попытками."""
    return client.chat.completions.create(**kwargs)
```

### Оптимизация токенов
```python
def count_tokens(text: str, model: str = "gpt-4") -> int:
    """Подсчет количества токенов в тексте."""
    import tiktoken
    
    encoding = tiktoken.encoding_for_model(model)
    return len(encoding.encode(text))

def truncate_text(text: str, max_tokens: int, model: str = "gpt-4") -> str:
    """Обрезка текста до максимального количества токенов."""
    import tiktoken
    
    encoding = tiktoken.encoding_for_model(model)
    tokens = encoding.encode(text)
    
    if len(tokens) <= max_tokens:
        return text
        
    truncated_tokens = tokens[:max_tokens]
    return encoding.decode(truncated_tokens)
```

## Telegram Bot API

### Настройка бота
```python
from telegram import Update, Bot
from telegram.ext import Application, CommandHandler, MessageHandler, filters
import logging

class TelegramBot:
    """Основной класс Telegram бота."""
    
    def __init__(self, token: str):
        """Инициализация бота.
        
        Args:
            token: Токен Telegram бота
        """
        self.token = token
        self.application = Application.builder().token(token).build()
        self.setup_handlers()
        
    def setup_handlers(self):
        """Настройка обработчиков команд и сообщений."""
        # Команды
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        
        # Обработчики медиа
        self.application.add_handler(MessageHandler(filters.PHOTO, self.handle_photo))
        self.application.add_handler(MessageHandler(filters.VIDEO, self.handle_video))
        self.application.add_handler(MessageHandler(filters.AUDIO, self.handle_audio))
        
        # Обработчик ошибок
        self.application.add_error_handler(self.error_handler)
```

### Обработка файлов
```python
async def download_file(update: Update, file_type: str) -> str:
    """Загрузка файла из Telegram.
    
    Args:
        update: Объект обновления Telegram
        file_type: Тип файла (photo, video, audio)
        
    Returns:
        Путь к загруженному файлу
    """
    try:
        if file_type == "photo":
            file_obj = update.message.photo[-1]  # Берем самое большое фото
        elif file_type == "video":
            file_obj = update.message.video
        elif file_type == "audio":
            file_obj = update.message.audio
        else:
            raise ValueError(f"Неподдерживаемый тип файла: {file_type}")
            
        # Получение файла
        file = await update.get_bot().get_file(file_obj.file_id)
        
        # Создание уникального имени файла
        file_extension = file.file_path.split('.')[-1]
        local_filename = f"temp/{file_obj.file_id}.{file_extension}"
        
        # Загрузка файла
        await file.download_to_drive(local_filename)
        
        logger.info(f"Файл загружен: {local_filename}")
        return local_filename
        
    except Exception as e:
        logger.error(f"Ошибка загрузки файла: {e}")
        raise
```

### Отправка ответов
```python
async def send_response(update: Update, text: str, parse_mode: str = "Markdown"):
    """Отправка ответа пользователю.
    
    Args:
        update: Объект обновления Telegram
        text: Текст сообщения
        parse_mode: Режим парсинга (Markdown, HTML)
    """
    try:
        # Разбиение длинных сообщений
        max_length = 4096
        if len(text) <= max_length:
            await update.message.reply_text(text, parse_mode=parse_mode)
        else:
            # Отправка по частям
            for i in range(0, len(text), max_length):
                chunk = text[i:i + max_length]
                await update.message.reply_text(chunk, parse_mode=parse_mode)
                
    except Exception as e:
        logger.error(f"Ошибка отправки сообщения: {e}")
        # Отправка сообщения об ошибке без форматирования
        await update.message.reply_text("Произошла ошибка при обработке запроса.")
```

## Обработка ошибок и логирование

### Централизованная обработка ошибок
```python
import functools
from typing import Callable, Any

def handle_errors(func: Callable) -> Callable:
    """Декоратор для обработки ошибок в обработчиках."""
    @functools.wraps(func)
    async def wrapper(update: Update, context, *args, **kwargs) -> Any:
        try:
            return await func(update, context, *args, **kwargs)
        except FileNotFoundError:
            await update.message.reply_text("Файл не найден.")
            logger.error(f"Файл не найден в {func.__name__}")
        except ValueError as e:
            await update.message.reply_text(f"Ошибка обработки: {str(e)}")
            logger.error(f"ValueError в {func.__name__}: {e}")
        except Exception as e:
            await update.message.reply_text("Произошла внутренняя ошибка.")
            logger.error(f"Неожиданная ошибка в {func.__name__}: {e}", exc_info=True)
    return wrapper

# Использование
@handle_errors
async def handle_photo(update: Update, context):
    """Обработчик фотографий."""
    # Логика обработки
    pass
```

### Структурированное логирование
```python
import structlog
import json

def setup_structured_logging():
    """Настройка структурированного логирования."""
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

# Использование
logger = structlog.get_logger()

logger.info("Обработка файла", 
           file_id="BAADBAADrwADBREAAYag2eLPt_RJAG", 
           file_type="photo",
           user_id=123456789)
```

## Кэширование и оптимизация

### Redis кэш для результатов
```python
import redis
import json
import hashlib
from typing import Optional, Any

class CacheManager:
    """Менеджер кэширования результатов."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        """Инициализация Redis клиента."""
        self.redis_client = redis.from_url(redis_url)
        
    def _generate_key(self, prefix: str, data: Any) -> str:
        """Генерация ключа кэша."""
        data_str = json.dumps(data, sort_keys=True)
        hash_obj = hashlib.md5(data_str.encode())
        return f"{prefix}:{hash_obj.hexdigest()}"
        
    def get(self, prefix: str, data: Any) -> Optional[str]:
        """Получение данных из кэша."""
        key = self._generate_key(prefix, data)
        cached_data = self.redis_client.get(key)
        return cached_data.decode() if cached_data else None
        
    def set(self, prefix: str, data: Any, value: str, ttl: int = 3600):
        """Сохранение данных в кэш."""
        key = self._generate_key(prefix, data)
        self.redis_client.setex(key, ttl, value)

# Использование
cache = CacheManager()

def cached_image_analysis(image_path: str) -> str:
    """Анализ изображения с кэшированием."""
    # Проверка кэша
    cached_result = cache.get("image_analysis", {"path": image_path})
    if cached_result:
        logger.info("Результат получен из кэша")
        return cached_result
        
    # Анализ изображения
    result = openai_client.analyze_image(image_path)
    
    # Сохранение в кэш
    cache.set("image_analysis", {"path": image_path}, result, ttl=7200)
    
    return result
```

## Мониторинг производительности

### Метрики времени выполнения
```python
import time
import functools
from typing import Dict, List

class PerformanceMonitor:
    """Мониторинг производительности."""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        
    def time_function(self, func_name: str):
        """Декоратор для измерения времени выполнения."""
        def decorator(func):
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    execution_time = time.time() - start_time
                    self.record_metric(func_name, execution_time)
            return wrapper
        return decorator
        
    def record_metric(self, name: str, value: float):
        """Запись метрики."""
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(value)
        
        # Ограничение размера истории
        if len(self.metrics[name]) > 1000:
            self.metrics[name] = self.metrics[name][-1000:]
            
    def get_stats(self, name: str) -> Dict[str, float]:
        """Получение статистики по метрике."""
        if name not in self.metrics or not self.metrics[name]:
            return {}
            
        values = self.metrics[name]
        return {
            "count": len(values),
            "avg": sum(values) / len(values),
            "min": min(values),
            "max": max(values)
        }

# Использование
monitor = PerformanceMonitor()

@monitor.time_function("image_analysis")
async def analyze_image_with_monitoring(image_path: str) -> str:
    """Анализ изображения с мониторингом."""
    return await openai_client.analyze_image(image_path)
```