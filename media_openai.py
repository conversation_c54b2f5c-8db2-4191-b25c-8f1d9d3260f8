"""
Модуль для работы с OpenAI API
Содержит функции для генерации и редактирования изображений
"""

import logging
import httpx
import asyncio
from typing import Optional
from media_config import OPENAI_API_KEY, OPENAI_MODEL

logger = logging.getLogger(__name__)


async def generate_image(prompt: str) -> Optional[bytes]:
    """
    Генерирует изображение по текстовому описанию через OpenAI API
    
    Args:
        prompt: Текстовое описание для генерации изображения
        
    Returns:
        bytes: Данные изображения в формате PNG или None при ошибке
    """
    if not prompt or not prompt.strip():
        logger.warning("Пустой prompt для генерации изображения")
        return None
    
    # Подготавливаем данные для запроса
    headers = {
        "Authorization": f"Bearer {OPENAI_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": OPENAI_MODEL,
        "prompt": prompt.strip(),
        "n": 1,
        "size": "1024x1024",
        "response_format": "b64_json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Логируем детали запроса (без чувствительных данных)
            logger.info(f"Отправляем запрос на генерацию изображения")
            logger.debug(f"Параметры запроса: model={OPENAI_MODEL}, prompt='{prompt[:100]}...', size={payload['size']}")

            response = await client.post(
                "https://cody.su/api/v1/images/generations",
                headers=headers,
                json=payload
            )

            # Логируем ответ API
            logger.debug(f"Получен ответ от OpenAI API: status_code={response.status_code}")

            if response.status_code == 200:
                result = response.json()

                # Логируем структуру ответа (без base64 данных)
                if "data" in result:
                    logger.debug(f"Ответ содержит {len(result['data'])} изображения(й)")

                if "data" in result and len(result["data"]) > 0:
                    import base64
                    image_data = result["data"][0]["b64_json"]
                    image_bytes = base64.b64decode(image_data)

                    logger.info(f"Изображение успешно сгенерировано, размер: {len(image_bytes)} байт")

                    # Логируем дополнительную информацию из ответа
                    if "created" in result:
                        logger.debug(f"Время создания изображения: {result['created']}")

                    return image_bytes
                else:
                    logger.error("Некорректный ответ от OpenAI API: отсутствуют данные изображения")
                    logger.debug(f"Структура ответа: {list(result.keys()) if result else 'пустой ответ'}")
                    return None
            else:
                error_text = response.text[:500] if response.text else "нет текста ошибки"
                logger.error(f"Ошибка OpenAI API: {response.status_code} - {error_text}")

                # Попытаемся распарсить JSON ошибки для более детального логирования
                try:
                    error_json = response.json()
                    if "error" in error_json:
                        error_info = error_json["error"]
                        logger.error(f"Детали ошибки: type={error_info.get('type')}, code={error_info.get('code')}")
                except:
                    pass

                return None
                
    except httpx.TimeoutException:
        logger.error("Таймаут при запросе к OpenAI API")
        return None
    except httpx.RequestError as e:
        logger.error(f"Ошибка сети при запросе к OpenAI API: {e}")
        return None
    except Exception as e:
        logger.error(f"Неожиданная ошибка при генерации изображения: {e}")
        return None


async def edit_images(prompt: str, images: list[bytes]) -> Optional[bytes]:
    """
    Редактирует изображения по текстовому описанию через OpenAI API

    Args:
        prompt: Текстовое описание изменений
        images: Список изображений в байтах для редактирования

    Returns:
        bytes: Данные отредактированного изображения или None при ошибке
    """
    if not prompt or not prompt.strip():
        logger.warning("Пустой prompt для редактирования изображения")
        return None

    if not images or len(images) == 0:
        logger.warning("Нет изображений для редактирования")
        return None

    # Подготавливаем заголовки
    headers = {
        "Authorization": f"Bearer {OPENAI_API_KEY}"
    }

    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Логируем детали запроса на редактирование
            logger.info(f"Отправляем запрос на редактирование изображения")
            logger.debug(f"Параметры запроса: model={OPENAI_MODEL}, prompt='{prompt[:100]}...', количество изображений={len(images)}")

            # Логируем размеры изображений (без самих данных)
            image_sizes = [len(img) for img in images]
            logger.debug(f"Размеры изображений: {image_sizes} байт")

            # Подготавливаем multipart/form-data
            files = {}
            data = {
                "model": OPENAI_MODEL,
                "prompt": prompt.strip(),
                "n": 1,
                "size": "1024x1024",
                "response_format": "b64_json"
            }

            # Добавляем изображения в форму
            for i, image_bytes in enumerate(images):
                files[f"image"] = (f"image_{i}.png", image_bytes, "image/png")
                logger.debug(f"Добавлено изображение {i+1} размером {len(image_bytes)} байт")
                # Для API редактирования обычно используется только первое изображение
                # Если нужно обработать несколько изображений, делаем отдельные запросы
                break

            response = await client.post(
                "https://cody.su/api/v1/images/edits",
                headers=headers,
                data=data,
                files=files
            )

            # Логируем ответ API
            logger.debug(f"Получен ответ от OpenAI API для редактирования: status_code={response.status_code}")

            if response.status_code == 200:
                result = response.json()

                # Логируем структуру ответа (без base64 данных)
                if "data" in result:
                    logger.debug(f"Ответ содержит {len(result['data'])} отредактированное(ых) изображения(й)")

                if "data" in result and len(result["data"]) > 0:
                    import base64
                    image_data = result["data"][0]["b64_json"]
                    image_bytes = base64.b64decode(image_data)

                    logger.info(f"Изображение успешно отредактировано, размер: {len(image_bytes)} байт")

                    # Логируем дополнительную информацию из ответа
                    if "created" in result:
                        logger.debug(f"Время создания отредактированного изображения: {result['created']}")

                    return image_bytes
                else:
                    logger.error("Некорректный ответ от OpenAI API: отсутствуют данные изображения")
                    logger.debug(f"Структура ответа: {list(result.keys()) if result else 'пустой ответ'}")
                    return None
            else:
                error_text = response.text[:500] if response.text else "нет текста ошибки"
                logger.error(f"Ошибка OpenAI API при редактировании: {response.status_code} - {error_text}")

                # Попытаемся распарсить JSON ошибки для более детального логирования
                try:
                    error_json = response.json()
                    if "error" in error_json:
                        error_info = error_json["error"]
                        logger.error(f"Детали ошибки редактирования: type={error_info.get('type')}, code={error_info.get('code')}")
                except:
                    pass

                return None

    except httpx.TimeoutException:
        logger.error("Таймаут при запросе к OpenAI API для редактирования")
        return None
    except httpx.RequestError as e:
        logger.error(f"Ошибка сети при запросе к OpenAI API для редактирования: {e}")
        return None
    except Exception as e:
        logger.error(f"Неожиданная ошибка при редактировании изображения: {e}")
        return None
