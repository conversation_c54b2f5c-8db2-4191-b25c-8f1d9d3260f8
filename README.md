# sh: Media - Telegram Bot

Минималистичный Telegram-бот для генерации и редактирования изображений с помощью OpenAI API (модель `gpt-image-1`).

## Возможности

### Генерация изображений
- **Команда**: `/img <описание>`
- **Пример**: `/img кот-космонавт в скафандре на фоне звезд`
- **Результат**: Создает новое изображение размером 1024x1024 пикселей

### Редактирование изображений
- **Способ 1**: Ответьте на сообщение с изображением командой `/img <описание изменений>`
- **Способ 2**: Прикрепите изображение к сообщению с командой `/img <описание изменений>`
- **Пример**: `/img добавить солнечные очки и улыбку`
- **Результат**: Отредактированное изображение на основе исходного

### Дополнительные возможности
- **Автоматическое определение режима**: Бот сам определяет, нужно генерировать или редактировать изображение
- **Обработка ошибок**: Информативные сообщения об ошибках и ограничениях
- **Подробное логирование**: Отслеживание всех операций для диагностики

## Установка и запуск

### Требования

- Python 3.8+
- Telegram Bot Token
- OpenAI API ключ (уже настроен в проекте)

### Установка зависимостей

```bash
pip install -r requirements.txt
```

Или установите зависимости вручную:
```bash
pip install aiogram httpx
```

### Настройка

1. Получите токен бота у [@BotFather](https://t.me/BotFather) в Telegram
2. Откройте файл `media_config.py`
3. Вставьте ваш токен в переменную `TELEGRAM_BOT_TOKEN`:

```python
TELEGRAM_BOT_TOKEN = "ваш_токен_здесь"
```

### Запуск

```bash
python media_bot.py
```

## Использование

1. Запустите бота командой `/start`
2. Для генерации изображения: `/img кот-космонавт`
3. Для редактирования: ответьте на сообщение с фото командой `/img добавить солнечные очки`

## Структура проекта

```
media_config.py      # Токены и константы
media_bot.py         # Точка входа, Router, запуск
media_handlers.py    # Обработчики команд и сообщений (будет добавлен)
media_openai.py      # Обёртки над images.generate / images.edit (будет добавлен)
media_utils.py       # Вспомогательные функции (будет добавлен)
```

## Статус разработки

- ✅ **Этап 1**: Инициализация проекта
- ✅ **Этап 2**: Генерация изображений
- ✅ **Этап 3**: Редактирование изображений
- ⏳ **Этап 4**: Завершение и деплой

## Лицензия

MIT License
