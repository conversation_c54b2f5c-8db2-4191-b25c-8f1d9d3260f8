---
inclusion: manual
description: "Инструкции для деплоя медиа-бота в продакшн"
---

# Инструкции по деплою медиа-бота

## Подготовка к деплою

### 1. Проверка кода
```bash
# Запуск линтеров
black --check .
flake8 .
mypy .

# Запуск тестов
pytest tests/ --cov=src --cov-fail-under=80
```

### 2. Проверка зависимостей
```bash
# Обновление requirements.txt
pip freeze > requirements.txt

# Проверка уязвимостей
safety check
```

### 3. Переменные окружения для продакшн

Создайте файл `.env.production`:
```bash
# Обязательные переменные
TELEGRAM_BOT_TOKEN=your_production_bot_token
OPENAI_API_KEY=your_openai_api_key

# Конфигурация продакшн
LOG_LEVEL=INFO
MAX_FILE_SIZE=52428800  # 50MB
ENVIRONMENT=production

# База данных (если используется)
DATABASE_URL=postgresql://user:pass@host:port/dbname

# Мониторинг
SENTRY_DSN=your_sentry_dsn
```

## Деплой на сервер

### Вариант 1: Docker деплой

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Установка системных зависимостей
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Копирование и установка Python зависимостей
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Копирование кода приложения
COPY . .

# Создание пользователя для безопасности
RUN useradd -m -u 1000 botuser && chown -R botuser:botuser /app
USER botuser

# Команда запуска
CMD ["python", "media_bot.py"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  media-bot:
    build: .
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

#### Команды деплоя
```bash
# Сборка и запуск
docker-compose -f docker-compose.yml up -d --build

# Просмотр логов
docker-compose logs -f media-bot

# Остановка
docker-compose down
```

### Вариант 2: Systemd сервис

#### Создание сервиса
```bash
sudo nano /etc/systemd/system/media-bot.service
```

```ini
[Unit]
Description=Media Bot Service
After=network.target

[Service]
Type=simple
User=botuser
WorkingDirectory=/opt/media-bot
Environment=PATH=/opt/media-bot/venv/bin
ExecStart=/opt/media-bot/venv/bin/python media_bot.py
Restart=always
RestartSec=10

# Переменные окружения
EnvironmentFile=/opt/media-bot/.env.production

# Логирование
StandardOutput=journal
StandardError=journal
SyslogIdentifier=media-bot

[Install]
WantedBy=multi-user.target
```

#### Управление сервисом
```bash
# Перезагрузка конфигурации systemd
sudo systemctl daemon-reload

# Запуск сервиса
sudo systemctl start media-bot

# Включение автозапуска
sudo systemctl enable media-bot

# Проверка статуса
sudo systemctl status media-bot

# Просмотр логов
sudo journalctl -u media-bot -f
```

## Мониторинг и логирование

### 1. Настройка логирования
```python
import logging
import sys
from logging.handlers import RotatingFileHandler

def setup_logging():
    """Настройка логирования для продакшн."""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Ротация логов
    file_handler = RotatingFileHandler(
        'logs/media_bot.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # Консольный вывод
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(log_format))
    
    # Настройка корневого логгера
    logging.basicConfig(
        level=logging.INFO,
        handlers=[file_handler, console_handler]
    )
```

### 2. Health check endpoint
```python
from flask import Flask, jsonify
import threading

app = Flask(__name__)

@app.route('/health')
def health_check():
    """Endpoint для проверки здоровья сервиса."""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat()
    })

def run_health_server():
    """Запуск health check сервера в отдельном потоке."""
    app.run(host='0.0.0.0', port=8080, debug=False)

# В main функции
health_thread = threading.Thread(target=run_health_server, daemon=True)
health_thread.start()
```

### 3. Интеграция с Sentry
```python
import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration

def setup_sentry():
    """Настройка Sentry для отслеживания ошибок."""
    sentry_logging = LoggingIntegration(
        level=logging.INFO,
        event_level=logging.ERROR
    )
    
    sentry_sdk.init(
        dsn=os.getenv('SENTRY_DSN'),
        integrations=[sentry_logging],
        traces_sample_rate=0.1,
        environment=os.getenv('ENVIRONMENT', 'development')
    )
```

## Резервное копирование

### Скрипт бэкапа
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/media-bot"
DATE=$(date +%Y%m%d_%H%M%S)

# Создание директории бэкапа
mkdir -p "$BACKUP_DIR"

# Бэкап кода
tar -czf "$BACKUP_DIR/code_$DATE.tar.gz" /opt/media-bot --exclude=venv --exclude=logs

# Бэкап логов
tar -czf "$BACKUP_DIR/logs_$DATE.tar.gz" /opt/media-bot/logs

# Удаление старых бэкапов (старше 30 дней)
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### Cron задача для автоматического бэкапа
```bash
# Добавить в crontab
0 2 * * * /opt/media-bot/backup.sh >> /var/log/media-bot-backup.log 2>&1
```

## Обновление в продакшн

### 1. Подготовка
```bash
# Создание бэкапа перед обновлением
./backup.sh

# Тестирование на staging окружении
docker-compose -f docker-compose.staging.yml up -d
```

### 2. Blue-Green деплой
```bash
# Запуск новой версии на другом порту
docker-compose -f docker-compose.green.yml up -d

# Переключение трафика через nginx/load balancer
# Остановка старой версии
docker-compose -f docker-compose.blue.yml down
```

### 3. Откат в случае проблем
```bash
# Быстрый откат к предыдущей версии
docker-compose -f docker-compose.blue.yml up -d
docker-compose -f docker-compose.green.yml down
```

## Безопасность

### 1. Настройка файрвола
```bash
# Разрешить только необходимые порты
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### 2. SSL сертификаты
```bash
# Установка certbot
sudo apt install certbot

# Получение сертификата
sudo certbot certonly --standalone -d yourdomain.com
```

### 3. Ограничение ресурсов
```yaml
# В docker-compose.yml
services:
  media-bot:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

## Чек-лист деплоя

- [ ] Код прошел все тесты
- [ ] Обновлены зависимости
- [ ] Настроены переменные окружения
- [ ] Создан бэкап текущей версии
- [ ] Настроено логирование
- [ ] Настроен мониторинг
- [ ] Проверена работа health check
- [ ] Настроено автоматическое резервное копирование
- [ ] Документация обновлена
- [ ] Команда уведомлена о деплое